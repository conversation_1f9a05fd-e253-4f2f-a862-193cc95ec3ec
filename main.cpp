#include <iostream>
#include <graphics.h>
#include <string>
#include <vector>
#include "Timer.h"
#pragma comment(lib,"Winmm.lib")
#pragma comment(lib,"MSIMG32.LIB")
using namespace std;
const int FPS = 144;
const int WINDOW_WIDTH = 1280;
const int WINDOW_HEIGHT = 720;
const int BUTTON_WIDTH = 192;
const int BUTTON_HEIGHT = 75;
const double PI = 3.141592653;
bool running = true;
bool is_start = false;
int counter = 0;
wstring music_command;
inline void putimage_alpha(int x, int y, IMAGE* img)
{
	int width = img->getwidth();
	int height = img->getheight();
	AlphaBlend(GetImageHDC(NULL), x, y, width, height, GetImageHDC(img), 0, 0, width, height, { AC_SRC_OVER,0,255,AC_SRC_ALPHA });

}
inline double power(double n)
{
	return n * n;
}
class Button
{
public:
	Button(RECT size, LPCTSTR img_path_idle, LPCTSTR img_path_hover, LPCTSTR img_path_push)
	{
		loadimage(&img_idle, img_path_idle);
		loadimage(&img_hover, img_path_hover);
		loadimage(&img_push, img_path_push);
		img_position = size;
		button_width = img_idle.getwidth();
		button_height = img_idle.getheight();
	}

	~Button()
	{

	}
	virtual void OnClick() = 0;
	void ProcessMessage(ExMessage& msg)
	{
		switch (msg.message)
		{
		case WM_MOUSEMOVE:
			if (status == Status::Idle && IsCheckRect(msg.x, msg.y))
			{
				status = Status::Hover;
			}
			else if (status == Status::Hover && !IsCheckRect(msg.x, msg.y))
			{
				status = Status::Idle;
			}
			break;
		case WM_LBUTTONDOWN:
			if (IsCheckRect(msg.x, msg.y))
			{
				status = Status::Push;
			}
			break;
		case WM_LBUTTONUP:
			if (status == Status::Push)
			{
				OnClick();
			}
			break;
		default:
			break;
		}
	}
	void Draw()
	{
		switch (status)
		{
		case Idle:
			putimage_alpha(img_position.left, img_position.top, &img_idle);
			break;
		case Hover:
			putimage_alpha(img_position.left, img_position.top, &img_hover);
			break;
		case Push:
			putimage_alpha(img_position.left, img_position.top, &img_push);
			break;
		dafault:
			break;
		}
	}
public:
	int button_width;
	int button_height;
private:
	enum Status {
		Idle = 0,
		Hover,
		Push
	};
private:
	RECT img_position;
	IMAGE img_idle;
	IMAGE img_hover;
	IMAGE img_push;
	Status status = Status::Idle;
	bool IsCheckRect(int x, int y)
	{
		return x > img_position.left && x<img_position.right && y>img_position.top && y < img_position.bottom;
}
};
class StartButton : public Button
{
	using Button::Button;
	void OnClick()
	{
		is_start = true;
		mciSendString(_T("play bgm repeat from 0"), NULL, 0, NULL);
	}
};
class OverButton : public Button
{
	using Button::Button;
	void OnClick()
	{
		running = false;
	}
};
class Altas
{
public:
	Altas(wstring path, int num)
	{
		for (int i = 0; i < num; i++)
		{
			IMAGE* frame = new IMAGE;
			wstring path_all = path + to_wstring(i) + L".png";
			loadimage(frame, path_all.c_str());
			frame_all.push_back(frame);
		}
	}
	~Altas()
	{
		for (int i = 0; i < frame_all.size(); i++)
		{
			delete frame_all[i];
		}
	}
	vector<IMAGE*> frame_all;
};
Altas* enemy_altas_left;
Altas* enemy_altas_right;
Altas* player_altas_left;
Altas* player_altas_right;//��Ԫ���
class Animation
{
public:
	Animation(Altas* altas, int interval)
	{
		intervals = interval;
		altas_share = altas;
	}//Ϊÿ������֡�����ڴ沢�ڼ���ͼƬ�����vector
	~Animation()
	{
	}
	void Display(int x,int y,int delta)
	{
		timer += delta;
		if (timer > intervals)
		{
			index_animation = (index_animation + 1) % altas_share->frame_all.size();
			timer = 0;
		}

		putimage_alpha(x, y, altas_share->frame_all[index_animation]);
	}//ȷ����ǰ����֡
	Altas* altas_share;
private:
	int intervals=0;
	int timer=0;
	int index_animation=0;
};
class Player
{
public:
	Player()
	{
		loadimage(&shadow, L"img/shadow_player.png");
		player_left = new Animation(player_altas_left, 45);
		player_right = new Animation(player_altas_right, 45);
		cooldown_1.set_timing(1000);
	}
	~Player()
	{
		delete player_left;
		delete player_right;
	}
	void Draw(int delta)
	{
		shadow_position.x = player_position.x + (PLAYER_WIDTH / 2 - SHADOW_WIDTH / 2);
		shadow_position.y = player_position.y + PLAYER_HEIGHT - EXCURSION;
		putimage_alpha(shadow_position.x, shadow_position.y, &shadow);//������Ӱ
		static bool dir_left = false;
		int dir = Right - Left;
		if (dir > 0)
		{
			dir_left = false;
		}
		else if (dir < 0)
		{
			dir_left = true;
		}
		if (dir_left)
		{
			player_left->Display(player_position.x, player_position.y, delta);
		}
		else
		{
			player_right->Display(player_position.x, player_position.y, delta);
		}//����������Ҷ���
}
	void ProcessEvent()
	{
		peekmessage(& msg);
		if (msg.message == WM_KEYDOWN)
		{
			if (msg.vkcode == 0x51)
			{
				Skill_One();
			}
		}


		if (msg.message == WM_KEYDOWN)
		{
			switch (msg.vkcode)
			{
			case 0x57:
				up = true;
				break;
			case 0x41:
				Left = true;
				break;
			case 0x44:
				Right = true;
				break;
			case 0x53:
				down = true;
				break;
			}
		}
		else if (msg.message == WM_KEYUP)
		{
			switch (msg.vkcode)
			{
			case 0x57:
				up = false;
				break;
			case 0x41:
				Left = false;
				break;
			case 0x44:
				Right = false;
				break;
			case 0x53:
				down = false;
				break;
			}
		}
		int counter = 0;
		double factor = 1.0;
		if (up == true)
		{
			counter++;
		}
		if (down == true)
		{
			counter++;
		}
		if (Left == true)
		{
			counter++;
		}
		if (Right == true)
		{
			counter++;
		}
		if (counter == 2)
		{
			factor = sqrt(1.0 / 2.0);
		}
		else
		{
			factor = 1.0;
		}
		if (up && player_position.y - int(PLAYER_SPEED * factor) > 0)
		{
			player_position.y -= int(PLAYER_SPEED * factor);
		}
		if (down && player_position.y + int(PLAYER_SPEED * factor) <= WINDOW_HEIGHT - PLAYER_HEIGHT)
		{
			player_position.y += int(PLAYER_SPEED * factor);
		}
		if (Left && player_position.x - int(PLAYER_SPEED * factor) > 0)
		{
			player_position.x -= int(PLAYER_SPEED * factor);
		}
		if (Right && player_position.x + int(PLAYER_SPEED * factor) <= WINDOW_WIDTH - PLAYER_WIDTH)
		{
			player_position.x += int(PLAYER_SPEED * factor);
		}

	}
	void ScoreUp(int up=1)
	{
		score++;
	}
	void DisplayScore()
	{
		static TCHAR score_str [100];
		_stprintf_s(score_str, sizeof(score_str), _T("��ҵĵ÷�Ϊ %d"), score);
		settextcolor(RGB(255, 85, 185));
		setbkmode(TRANSPARENT);
		outtextxy(10, 10, score_str);
	}//˫�˿�����p1\p2�Ĳ�����Ϊ�ж��߼�
	POINT GetPosition() const
	{
		return player_position;
	}
	int GetWidth() const
	{
		return PLAYER_WIDTH;
	}
	int GetHeight() const
	{
		return PLAYER_HEIGHT;
	}
	void Skill_One()
	{

	}
private:
	const int PLAYER_ANIMATION_NUM = 6;
	int PLAYER_SPEED = 3;
	const int PLAYER_WIDTH = 80;
	const int PLAYER_HEIGHT = 80;
	const int SHADOW_WIDTH = 32;
	const int EXCURSION = 8;
	POINT player_position = { 640,360 };
	POINT shadow_position = { 0,0 };
	Animation *player_left;
	Animation *player_right;
	bool up = false;
	bool down = false;
	bool Left = false;
	bool Right = false;
	IMAGE shadow;
	long int score = 0;
	ExMessage msg;
	Timer cooldown_1;
	bool if_cooldown_1 = true;
	Timer running_1;
	bool if_running_1 = true;
};
class Bullet
{
public:
	POINT position = { 200,200 };
	void Draw(const Player&player)
	{
		setlinecolor(RGB(255, 255, 50));
		setfillcolor(RGB(200, 75, 10));
		fillcircle(position.x, position.y, BULLET_SIZE);
	}
private:
	const int BULLET_SIZE = 10;
};
class Enemy
{
public:
	Enemy()
	{
		loadimage(&shadow, L"img/shadow_enemy.png");
		enemy_left = new Animation(enemy_altas_left, 45);
		enemy_right = new Animation(enemy_altas_right, 45);
		enum class Edge
		{
			up = 0,
		    down,
		    left,
			right
		};
		Edge init_edge = (Edge)(rand() % 4);
		switch (init_edge)
		{
		case Edge::up:
			enemy_position.x = rand() % WINDOW_WIDTH;
			enemy_position.y = -ENEMY_HEIGHT;
			break;
		case Edge::down:
			enemy_position.x = rand() % WINDOW_WIDTH;
			enemy_position.y = WINDOW_HEIGHT;
			break;
		case Edge::left:
			enemy_position.x = -ENEMY_WIDTH;
			enemy_position.y = rand() % WINDOW_HEIGHT;
			break;
		case Edge::right:
			enemy_position.x =  WINDOW_WIDTH;
			enemy_position.y = rand() % WINDOW_HEIGHT;
			break;
		 }
	}
	~Enemy()
	{
		delete enemy_left;
		delete enemy_right;
	}
	void Move(const Player& player)
	{
		POINT p_position = player.GetPosition();
		double len = sqrt(power((p_position.x-enemy_position.x))+power((p_position.y - enemy_position.y)));
		double cos = (double)(p_position.x - enemy_position.x)/len;
		double sin = (double)(p_position.y - enemy_position.y) / len;
		if (p_position.x - enemy_position.x > 0)
		{
			Right = true;
			Left = false;
		}
		if (p_position.x - enemy_position.x < 0)
		{
			Right = false;
			Left = true;
		}
		enemy_position.x += (int)ENEMY_SPEED * cos;
		enemy_position.y += (int)ENEMY_SPEED * sin;//ʸ���ƶ�׷��
}
	void Draw(int delta)
	{
		shadow_position.x = enemy_position.x + (ENEMY_WIDTH / 2 - SHADOW_WIDTH / 2);
		shadow_position.y = enemy_position.y + ENEMY_HEIGHT - EXCURSION;
		putimage_alpha(shadow_position.x, shadow_position.y, &shadow);//������Ӱ

		static bool dir_left = false;
		int dir = Right - Left;
		if (unmatchable == false)
		{
			if (dir > 0)
			{
				dir_left = false;
			}
			else if (dir < 0)
			{
				dir_left = true;
			}
			if (dir_left)
			{
				enemy_left->Display(enemy_position.x, enemy_position.y, delta);
			}
			else
			{
				enemy_right->Display(enemy_position.x, enemy_position.y, delta);
			}
		}
		else if (unmatchable == true)
		{
			if (dir > 0)
			{
				dir_left = false;
			}
			else if (dir < 0)
			{
				dir_left = true;
			}
			if (dir_left)
			{
				enemy_left->Display(enemy_position.x, enemy_position.y, delta);
			}
			else
			{
				enemy_right->Display(enemy_position.x, enemy_position.y, delta);
			}
		}
	}
	bool CheckPlayerCollision(const Player& player)
	{
		POINT position = player.GetPosition();
		int player_width = player.GetWidth();
		int player_height = player.GetHeight();
		POINT checkpoint = { enemy_position.x + ENEMY_WIDTH / 2,enemy_position.y + ENEMY_HEIGHT / 2 };
		bool up_to_down = checkpoint.y > position.y && checkpoint.y < (position.y + player_height);
		bool left_to_right = checkpoint.x > position.x && checkpoint.x < (position.x + player_width);
		return up_to_down&&left_to_right;
	}
	bool CheckBulletCollision(const Bullet& bullet)
	{
		POINT position = bullet.position;
		bool up_to_down = position.y > enemy_position.y && position.y < (enemy_position.y + ENEMY_HEIGHT);
		bool left_to_right = position.x > enemy_position.x && position.x < (enemy_position.x + ENEMY_WIDTH);
		return up_to_down && left_to_right;
	}
	void clock()
	{
		if (unmatchable == true)
		{
			duration--;
			if (duration == 0)
			{
				unmatchable = false;
				duration = 20;
			}
		}
	}
	void Hurted()
	{
		if (unmatchable == false)
		{
			hp -= 1;
			unmatchable = true;
			switch (counter)
			{
			case 0:
				music_command = L"play hit1 from 0";
				break;
			case 1:
				music_command = L"play hit2 from 0";
				break;
			case 2:
				music_command = L"play hit3 from 0";
				break;
			case 3:
				music_command = L"play hit4 from 0";
				break;
			default:
				break;
			}
			counter = (++counter) % 4;
			mciSendString(music_command.c_str(), NULL, 0, NULL);
		}
		if (hp == 0)
		{
			alive = false;
		}
	}
	bool CheckAlive()
	{
		return alive;
	}

private:
	IMAGE shadow;
	Animation* enemy_left;
	Animation* enemy_right;
	const int ENEMY_WIDTH = 80;
	const int ENEMY_HEIGHT = 80;
	const int SHADOW_WIDTH = 48;
	const int ENEMY_SPEED = 2;
	const int EXCURSION = 25;
	POINT enemy_position = {100,100 };
	POINT shadow_position = { 0,0 };
	bool up = false;
	bool down = false;
	bool Left = false;
	bool Right = false;
	bool alive = true;
	int hp = 1;
	bool unmatchable = false;
	int duration = 20;
};
void MakeEnemy(vector<Enemy*>& enemy_list, int interval = 100)
{
	static int counter = 0;
	if ((counter++) % interval == 0)
	{
		enemy_list.push_back(new Enemy);
	}
	if (counter > 20000)
	{
		counter = 0;
	}
}
void UpdateBullet(vector<Bullet>& bullet_list, const Player& player)
{
	const double RADIAL_SPEED = 0.0045;
	const double TENGENTIAL_SPEED = 0.0055;
	double bullet_interval = 2 * PI / bullet_list.size();//��ʼ���ȼ��
	double radius = 100 + 25 * sin(GetTickCount() * RADIAL_SPEED);//�������ڲ����ľ���仯
	int width = player.GetWidth();
	int height = player.GetHeight();
	POINT p_position = player.GetPosition();
	for (int i = 0; i < bullet_list.size(); i++)
	{
		double tengential = bullet_interval * i + GetTickCount() * TENGENTIAL_SPEED;
		bullet_list[i].position.x = p_position.x+width/2+(int)(radius*sin(tengential));
		bullet_list[i].position.y = p_position.y+height/2+(int)(radius*cos(tengential));
	}
}
RECT GetButtonPosition(const int button_width,const int button_height,const int height)
{
	RECT temp;
	temp.left = WINDOW_WIDTH / 2 - button_width / 2;
	temp.right = temp.left + button_width;
	temp.top = height;
	temp.bottom = temp.top + button_height;
	return temp;
}
int main()
{
	ExMessage msg;

	initgraph(WINDOW_WIDTH, WINDOW_HEIGHT);
	IMAGE img_background;
	IMAGE img_ui;
	loadimage(&img_background, _T("img/background.png"));
	loadimage(&img_ui, _T("img/menu.png"));
	StartButton start_button(GetButtonPosition(BUTTON_WIDTH, BUTTON_HEIGHT, 430), _T("img/ui_start_idle.png"), _T("img/ui_start_hovered.png"), _T("img/ui_start_pushed.png"));
	OverButton over_button(GetButtonPosition(BUTTON_WIDTH, BUTTON_HEIGHT, 550), _T("img/ui_quit_idle.png"), _T("img/ui_quit_hovered.png"), _T("img/ui_quit_pushed.png"));
	enemy_altas_left = new Altas(L"img/enemy_left_", 6);
	enemy_altas_right = new Altas(L"img/enemy_right_", 6);
	player_altas_left = new Altas(L"img/player_left_", 6);
	player_altas_right = new Altas(L"img/player_right_", 6);
	mciSendString(_T("open music/bgm.mp3 alias bgm"), NULL, 0, NULL);
	mciSendString(_T("open music/hit(1).mp3 alias hit1"), NULL, 0, NULL);
	mciSendString(_T("open music/hit(2).mp3 alias hit2"), NULL, 0, NULL);
	mciSendString(_T("open music/hit(3).mp3 alias hit3"), NULL, 0, NULL);
	mciSendString(_T("open music/hit(4).mp3 alias hit4"), NULL, 0, NULL);
	mciSendString(_T("play hit1 from 0"), NULL, 0, NULL);//�����ÿ�ʼ��Ч���棬��ֹ��һ�β��ŵĿ�������
	Player pm;
	vector<Bullet> bullet_all(6);
	vector<Enemy*> enemy_all;
	BeginBatchDraw();
	while (running)
	{
		cleardevice();
        DWORD start = GetTickCount();
		if (!is_start)
		{
			while (peekmessage(&msg))
			{
				start_button.ProcessMessage(msg);
				over_button.ProcessMessage(msg);
			}
		}
		if (!is_start)
		{
			putimage(0, 0, &img_ui);
			start_button.Draw();
			over_button.Draw();
		}
		else
		{
			putimage(0, 0, &img_background);
		}
		if (is_start)
		{
			pm.ProcessEvent();

			pm.Draw(10);
			UpdateBullet(bullet_all, pm);
			for (int i = 0; i < bullet_all.size(); i++)
			{
				bullet_all[i].Draw(pm);
			}
			MakeEnemy(enemy_all, 50);//ע���ֵ����Ϊ2�������ڴ�᲻��
			for (int i = 0; i < enemy_all.size(); i++)
			{
				enemy_all[i]->Move(pm);
			}
			for (int i = 0; i < enemy_all.size(); i++)
			{
				enemy_all[i]->Draw(10);
				if (enemy_all[i]->CheckPlayerCollision(pm))
				{
					MessageBox(GetHWnd(), _T("over"), _T("��Ϸʧ��"), MB_OK);
					running = false;
					break;
				}
			}

			for (int i = 0; i < enemy_all.size(); i++)
			{
				for (int j = 0; j < bullet_all.size(); j++)
				{
					if (enemy_all[i]->CheckBulletCollision(bullet_all[j]))
					{
						enemy_all[i]->Hurted();
						enemy_all[i]->clock();
					}
				}
			}

			for (int i = 0; i < enemy_all.size(); i++)
			{
				Enemy* temp = enemy_all[i];
				if (!enemy_all[i]->CheckAlive())
				{
					//enemy_all.erase(enemy_all.begin() + i);
					swap(enemy_all[i], enemy_all.back());
					enemy_all.pop_back();
					delete temp;
					pm.ScoreUp();
				}
			}
			//pm.DisplayScore();
		}
		FlushBatchDraw();


		DWORD end = GetTickCount();
		if ((end - start) < (1000 / FPS))
		{
			Sleep((1000 / FPS) - (end - start));
		}//������60֡
	}
	EndBatchDraw();
}
//�Ѷ��޸Ŀ�ͨ�����ӵ����ٶ�(Ѫ��)����������ٶȡ������ӵ�����������ˢ��Ƶ�ʡ������ܻ��޵�֡ʱ��ʵ��