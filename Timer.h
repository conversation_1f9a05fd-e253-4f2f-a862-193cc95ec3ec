#pragma once
#include <functional>
class Timer
{
public:
	Timer()=default;
	~Timer() = default;
	void reset()
	{
		pass_time = 0;
		int timing_duration = 0;
		bool paused = false;
		bool single_activate = false;
		bool if_hb_activated = false;
	}
	void set_timing(int time)
	{
		timing_duration = time;
	}
	void pause()
	{
		paused = true;
	}
	void resuem()
	{
		paused = false;
	}
	void set_logic(std::function<void()> logic)
	{
		wanted_logic = logic;
	}
	void timer_on(int delta)
	{
		if (paused)
		{
			return;
		}
		pass_time += delta;
		if (pass_time >= timing_duration)
		{
			if (!single_activate || (single_activate && !if_hb_activated) && wanted_logic)
			{
				wanted_logic();
				if_hb_activated = true;
				pass_time = 0;
			}
		}
	}
private:
	int pass_time = 0;
	int timing_duration = 0;
	bool paused = false;
	bool single_activate = false;
	bool if_hb_activated = false;
	std::function<void()> wanted_logic;
};

